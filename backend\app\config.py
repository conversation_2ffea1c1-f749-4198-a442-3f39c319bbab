import os
import json
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    # Set DEBUG based on environment variable, default to False for security
    DEBUG = os.getenv("FLASK_DEBUG", "False").lower() in ("true", "1", "t")
    SECRET_KEY = os.getenv("SECRET_KEY")
    if not SECRET_KEY:
        raise ValueError("No SECRET_KEY set in environment variables")

    SQLALCHEMY_DATABASE_URI = os.getenv("DATABASE_URL")
    if not SQLALCHEMY_DATABASE_URI:
        # For development, provide a default SQLite database
        if os.getenv("FLASK_ENV") != "production":
            SQLALCHEMY_DATABASE_URI = "sqlite:///app.db"
        else:
            raise ValueError("No DATABASE_URL set in environment variables")

    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Firebase credentials can be provided either as a path to a JSON file
    # or as a JSON string directly in the environment variables
    FIREBASE_CREDENTIALS_PATH = os.getenv("FIREBASE_CREDENTIALS_PATH")
    FIREBASE_CREDENTIALS_JSON = os.getenv("FIREBASE_CREDENTIALS_JSON")

    # Firebase Storage bucket name
    FIREBASE_STORAGE_BUCKET = os.getenv("FIREBASE_STORAGE_BUCKET")
    if not FIREBASE_STORAGE_BUCKET and os.getenv("FLASK_ENV") == "production":
        # In production, log warning but don't fail - allow app to start without Firebase
        import logging
        logging.warning("No Firebase Storage bucket specified. Firebase storage features will be disabled.")

    # Validate that at least one Firebase credential option is provided in production
    # Allow missing credentials in development for testing
    if not FIREBASE_CREDENTIALS_PATH and not FIREBASE_CREDENTIALS_JSON:
        if os.getenv("FLASK_ENV") == "production":
            # In production, log warning but don't fail - allow app to start without Firebase
            import logging
            logging.warning("No Firebase credentials provided. Firebase features will be disabled.")
        else:
            # In development, this is acceptable
            pass

    # Parse the JSON string if provided
    if FIREBASE_CREDENTIALS_JSON:
        try:
            FIREBASE_CREDENTIALS = json.loads(FIREBASE_CREDENTIALS_JSON)
        except json.JSONDecodeError:
            raise ValueError("Invalid JSON in FIREBASE_CREDENTIALS_JSON environment variable")

    LOG_FILE = os.getenv("LOG_FILE", "./logs/app.log")

    # Cache configuration
    CACHE_TYPE = os.getenv("CACHE_TYPE", "SimpleCache")  # Options: SimpleCache, RedisCache
    REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    CACHE_TIMEOUT = int(os.getenv("CACHE_TIMEOUT", 3600))  # Default: 1 hour
    CACHE_KEY_PREFIX = os.getenv("CACHE_KEY_PREFIX", "customer_mgmt_")

    # Cache version - increment this when making schema changes
    CACHE_VERSION = "1.0"

    # Rate limiting configuration (for custom rate limiter)
    # Default rate limits can be overridden per endpoint
    RATE_LIMIT_ENABLED = os.getenv("RATE_LIMIT_ENABLED", "True").lower() in ("true", "1", "t")

    # Security settings
    # Enable response sanitization by default to hide sensitive information
    SANITIZE_RESPONSES = os.getenv("SANITIZE_RESPONSES", "True").lower() in ("true", "1", "t")
    SESSION_TIMEOUT = int(os.getenv("SESSION_TIMEOUT", 3600))  # 1 hour default

    # Encryption is now handled by Render's encryption at rest
    # No need for application-level field encryption