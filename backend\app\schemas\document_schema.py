"""
Document schema module.
This module defines the schema for Document model validation.
"""
from marshmallow import fields, validates, ValidationError, validate
from app.schemas import ma
from app.models.document import Document, ALLOWED_DOCUMENT_TYPES

class DocumentSchema(ma.SQLAlchemySchema):
    """Schema for Document model."""
    
    class Meta:
        """Meta class for DocumentSchema."""
        model = Document
        load_instance = True
    
    id = ma.auto_field(dump_only=True)
    customer_id = fields.Integer(required=True)
    event_id = fields.Integer(allow_none=True)
    file_url = fields.Url(dump_only=True)
    file_path = fields.String(dump_only=True)
    name = fields.String(dump_only=True)
    document_type = fields.String(required=True, validate=validate.OneOf(ALLOWED_DOCUMENT_TYPES))
    uploaded_by = fields.Integer(required=True)
    expiry_date = fields.DateTime(allow_none=True)
    created_at = ma.auto_field(dump_only=True)
    status = fields.String(dump_only=True)
    related_document_id = fields.Integer(allow_none=True)
    
    # Additional fields for document upload
    file = fields.Raw(required=False, load_only=True)
    document_not_applicable = fields.Boolean(load_only=True)
    
    @validates('customer_id')
    def validate_customer_id(self, customer_id):
        """Validate customer_id field."""
        if not customer_id:
            raise ValidationError('Customer ID is required')

    @validates('document_type')
    def validate_document_type(self, document_type):
        """Validate document_type field."""
        if not document_type:
            raise ValidationError('Document type is required')
        if document_type not in ALLOWED_DOCUMENT_TYPES:
            raise ValidationError(f'Invalid document type. Must be one of {ALLOWED_DOCUMENT_TYPES}')

class DocumentUploadSchema(ma.Schema):
    """Schema for document upload."""

    customer_id = fields.Integer(required=True)
    event_id = fields.Integer(allow_none=True)
    document_type = fields.String(required=True, validate=validate.OneOf(ALLOWED_DOCUMENT_TYPES))
    expiry_date = fields.DateTime(allow_none=True)
    related_document_id = fields.Integer(allow_none=True)
    document_not_applicable = fields.Boolean()
    use_version_status = fields.Boolean(load_default=True)
    version_status = fields.String(validate=validate.OneOf(["active", "inactive", "not_applicable"]), load_default="active")
    
    @validates('document_type')
    def validate_document_type(self, document_type):
        """Validate document_type field."""
        if not document_type:
            raise ValidationError('Document type is required')
        if document_type not in ALLOWED_DOCUMENT_TYPES:
            raise ValidationError(f'Invalid document type. Must be one of {ALLOWED_DOCUMENT_TYPES}')

# Initialize schemas
document_schema = DocumentSchema()
documents_schema = DocumentSchema(many=True)
document_upload_schema = DocumentUploadSchema()
